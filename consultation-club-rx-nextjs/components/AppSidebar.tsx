'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  Building2,
  Package,
  Activity,
  Users,
  CreditCard,
  Settings,
  Bell,
  HelpCircle,
  LogOut
} from 'lucide-react';

const menuItems = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Clinics',
    url: '/clinics',
    icon: Building2,
  },
  {
    title: 'Inventory',
    url: '/inventory',
    icon: Package,
  },
  {
    title: 'Logs & Activity',
    url: '/logs-activity',
    icon: Activity,
  },
  {
    title: 'Users & Access',
    url: '/users-access',
    icon: Users,
  },
  {
    title: 'Plan & Billing',
    url: '/plan-billing',
    icon: CreditCard,
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
  },
  {
    title: 'Notifications',
    url: '/notifications',
    icon: Bell,
  },
  {
    title: 'Support',
    url: '/support',
    icon: HelpCircle,
  },
];

export function AppSidebar() {
  const pathname = usePathname();

  return (
    <Sidebar className="border-r border-gray-200">
      <SidebarContent className="bg-gray-900 text-white">
        <div className="p-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
              <span className="text-gray-900 font-bold text-sm">C</span>
            </div>
            <span className="font-semibold">consultation.club</span>
            <span className="text-xs bg-blue-600 px-2 py-1 rounded">Rx</span>
          </div>
        </div>
        
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.url}
                    className="text-gray-300 hover:text-white hover:bg-gray-800 data-[active=true]:bg-teal-600 data-[active=true]:text-white"
                  >
                    <Link href={item.url} className="flex items-center gap-3 px-3 py-2">
                      <item.icon className="w-5 h-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter className="bg-gray-900 text-white p-4">
        <div className="flex items-center gap-2 text-gray-300 hover:text-white cursor-pointer">
          <LogOut className="w-5 h-5" />
          <span>Log Out</span>
        </div>
        <div className="mt-4 text-xs text-gray-500">
          © 2025 Consultation.club Rx. All rights reserved.
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
