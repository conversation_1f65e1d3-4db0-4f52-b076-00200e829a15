/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
  // Enable static exports if needed
  // output: 'export',
  // trailingSlash: true,
  
  // Webpack configuration for better compatibility
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Handle any specific webpack configurations if needed
    return config;
  },
  
  // Environment variables
  env: {
    CUSTOM_KEY: 'consultation-club-rx',
  },
  
  // Redirects for maintaining URL structure
  async redirects() {
    return [
      // Add any redirects if needed for URL compatibility
    ];
  },
  
  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
