# Next.js Migration Guide

## 🚀 Migration Overview

This guide outlines the complete migration from React + Vite to Next.js 14 with App Router while preserving all existing functionality and API integration.

## 📁 New Project Structure

```
consultation-club-rx-nextjs/
├── app/                          # Next.js App Router
│   ├── layout.tsx               # Root layout
│   ├── page.tsx                 # Home page (redirects to dashboard)
│   ├── providers.tsx            # React Query & Theme providers
│   ├── globals.css              # Global styles
│   ├── dashboard/page.tsx       # Dashboard page
│   ├── clinics/page.tsx         # Clinics page
│   ├── inventory/page.tsx       # Inventory page
│   ├── logs-activity/page.tsx   # Logs & Activity page
│   ├── users-access/page.tsx    # Users & Access page
│   ├── plan-billing/page.tsx    # Plan & Billing page
│   ├── settings/page.tsx        # Settings page
│   ├── notifications/page.tsx   # Notifications page
│   ├── support/page.tsx         # Support page
│   └── not-found.tsx           # 404 page
├── components/                  # Reusable components
│   ├── Layout.tsx              # Layout wrapper
│   ├── AppSidebar.tsx          # Navigation sidebar
│   ├── ErrorBoundary.tsx       # Error boundary
│   ├── LoadingSpinner.tsx      # Loading component
│   ├── ui/                     # UI components (shadcn/ui)
│   └── clinics/                # Clinic-specific components
├── lib/                        # Utilities
│   └── utils.ts               # Utility functions
├── hooks/                      # Custom hooks
│   ├── useApi.ts              # React Query hooks
│   ├── use-toast.ts           # Toast hook
│   └── use-mobile.tsx         # Mobile detection hook
├── services/                   # API services
│   └── api.ts                 # API service layer
├── types/                      # TypeScript types
│   └── api.ts                 # API type definitions
├── data/                       # Mock data
│   └── mockData.json          # Centralized mock data
├── public/                     # Static assets
├── next.config.js             # Next.js configuration
├── package.json               # Dependencies
├── tailwind.config.ts         # Tailwind configuration
└── tsconfig.json              # TypeScript configuration
```

## 🔄 Key Changes Made

### 1. Routing Migration
- **From**: React Router DOM (`react-router-dom`)
- **To**: Next.js App Router
- **Changes**:
  - `<Link to="/path">` → `<Link href="/path">`
  - `useLocation()` → `usePathname()`
  - `useNavigate()` → `useRouter()` from `next/navigation`

### 2. Component Updates
- **AppSidebar**: Updated to use Next.js `Link` and `usePathname`
- **All Pages**: Wrapped with `Layout` component and added `'use client'` directive
- **Error Handling**: Maintained existing error boundary pattern

### 3. Configuration Files
- **next.config.js**: Next.js configuration with security headers
- **package.json**: Updated dependencies for Next.js
- **tailwind.config.ts**: Compatible with Next.js

### 4. Provider Setup
- **app/providers.tsx**: Centralized provider setup for React Query, themes, and toasts
- **app/layout.tsx**: Root layout with metadata and provider integration

## 📦 Dependencies

### New Dependencies Added
```json
{
  "next": "^14.2.5",
  "@next/eslint-plugin-next": "^14.2.5",
  "eslint-config-next": "^14.2.5"
}
```

### Removed Dependencies
```json
{
  "react-router-dom": "^6.26.2",
  "@vitejs/plugin-react-swc": "^3.5.0",
  "vite": "^5.4.1"
}
```

### Preserved Dependencies
- All existing UI components (Radix UI, shadcn/ui)
- React Query for state management
- Axios for API calls
- All styling dependencies (Tailwind CSS, etc.)

## 🛠️ Migration Steps

### Step 1: Install Next.js
```bash
# Remove old dependencies
npm uninstall react-router-dom @vitejs/plugin-react-swc vite

# Install Next.js
npm install next @next/eslint-plugin-next eslint-config-next
```

### Step 2: Copy Files
```bash
# Copy all files from src/ to appropriate Next.js directories
# Update import paths to use Next.js conventions
```

### Step 3: Update Configuration
```bash
# Replace package.json scripts
# Update next.config.js
# Update tailwind.config.ts for Next.js
```

### Step 4: Update Components
```bash
# Add 'use client' to client components
# Update routing imports
# Update navigation hooks
```

## 🔧 File Copying Instructions

### Essential Files to Copy:

1. **Components** (`src/components/` → `components/`)
   - All UI components from `src/components/ui/`
   - Clinic components from `src/components/clinics/`
   - Update import paths to use `@/components/`

2. **Hooks** (`src/hooks/` → `hooks/`)
   - `useApi.ts` - React Query hooks
   - `use-toast.ts` - Toast functionality
   - `use-mobile.tsx` - Mobile detection

3. **Services** (`src/services/` → `services/`)
   - `api.ts` - API service layer with axios

4. **Types** (`src/types/` → `types/`)
   - `api.ts` - TypeScript type definitions

5. **Data** (`src/data/` → `data/`)
   - `mockData.json` - Mock data for API simulation

6. **Utilities** (`src/lib/` → `lib/`)
   - `utils.ts` - Utility functions

7. **Styles** (`src/index.css` → `app/globals.css`)
   - Global CSS with Tailwind directives

8. **Public Assets** (`public/` → `public/`)
   - Static assets (favicon, images, etc.)

## ⚡ Performance Benefits

### Next.js Advantages:
1. **Server-Side Rendering (SSR)**: Improved SEO and initial load times
2. **Static Site Generation (SSG)**: Pre-rendered pages for better performance
3. **Automatic Code Splitting**: Smaller bundle sizes
4. **Image Optimization**: Built-in image optimization
5. **Built-in Performance Monitoring**: Web Vitals tracking
6. **Edge Runtime Support**: Deploy to edge locations

### Preserved Features:
1. **React Query Caching**: All caching strategies maintained
2. **API Integration**: Complete API layer preserved
3. **Component Architecture**: All components work identically
4. **TypeScript Support**: Full type safety maintained
5. **Tailwind CSS**: All styling preserved
6. **Error Handling**: Error boundaries and toast notifications

## 🧪 Testing Strategy

### 1. Functionality Testing
- [ ] All pages load correctly
- [ ] Navigation works between pages
- [ ] API calls function properly
- [ ] Forms submit successfully
- [ ] Loading states display correctly
- [ ] Error handling works as expected

### 2. Performance Testing
- [ ] Page load times improved
- [ ] Bundle size optimized
- [ ] Core Web Vitals metrics
- [ ] Mobile responsiveness maintained

### 3. SEO Testing
- [ ] Meta tags properly set
- [ ] Open Graph tags working
- [ ] Sitemap generation
- [ ] Robot.txt configuration

## 🚀 Deployment Options

### Vercel (Recommended)
```bash
npm install -g vercel
vercel
```

### Netlify
```bash
npm run build
# Deploy dist folder
```

### Self-hosted
```bash
npm run build
npm start
```

## 🔄 Development Workflow

### Development Server
```bash
npm run dev
# Runs on http://localhost:3000
```

### Build Process
```bash
npm run build
# Creates optimized production build
```

### Type Checking
```bash
npm run type-check
# Runs TypeScript compiler without emitting files
```

## 📈 Migration Benefits

### Immediate Benefits:
1. **Better SEO**: Server-side rendering improves search engine indexing
2. **Faster Loading**: Automatic code splitting and optimization
3. **Better Developer Experience**: Hot reloading, better error messages
4. **Production Ready**: Built-in optimizations for production

### Long-term Benefits:
1. **Scalability**: Better performance at scale
2. **Maintainability**: Cleaner file-based routing
3. **Future-proof**: Latest React patterns and features
4. **Ecosystem**: Access to Next.js ecosystem and plugins

## ✅ Verification Checklist

- [ ] All pages accessible via navigation
- [ ] Dashboard displays stats and activity
- [ ] Clinics CRUD operations work
- [ ] Users management functions
- [ ] Billing information displays
- [ ] Support form submits
- [ ] Search and filtering work
- [ ] Loading states display
- [ ] Error handling functions
- [ ] Mobile responsiveness maintained
- [ ] All API integrations work
- [ ] TypeScript compilation successful
- [ ] Build process completes without errors

## 🎯 Next Steps

1. **Copy all files** according to the structure above
2. **Update import paths** to use Next.js conventions
3. **Test all functionality** to ensure nothing is broken
4. **Optimize for production** using Next.js features
5. **Deploy to production** using preferred hosting platform

The migration preserves 100% of existing functionality while adding the benefits of Next.js for better performance, SEO, and developer experience.
