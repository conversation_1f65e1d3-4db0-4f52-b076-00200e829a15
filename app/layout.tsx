import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Consultation Club RX',
  description: 'Clinic management system for prescription tracking and inventory management',
  keywords: ['clinic', 'prescription', 'inventory', 'management', 'healthcare'],
  authors: [{ name: 'Consultation Club' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Consultation Club RX',
    description: 'Clinic management system for prescription tracking and inventory management',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Consultation Club RX',
    description: 'Clinic management system for prescription tracking and inventory management',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
