'use client';

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Building2, 
  Package, 
  Clock,
  Users,
  FileText,
  BarChart3,
  Settings,
  Bell,
  HelpCircle,
  LogOut,
  Download,
  Loader2,
  Check
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useBillingPlans, useInvoices, useUpgradePlan } from '@/hooks/useApi';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/Layout';

const PlanBilling = () => {
  const { toast } = useToast();

  // Fetch billing data using React Query
  const { 
    data: plansData, 
    isLoading: plansLoading, 
    isError: plansError 
  } = useBillingPlans();

  const { 
    data: invoicesData, 
    isLoading: invoicesLoading, 
    isError: invoicesError 
  } = useInvoices();

  const upgradePlanMutation = useUpgradePlan();

  const handleUpgradePlan = async (planId: number) => {
    try {
      await upgradePlanMutation.mutateAsync(planId);
      toast({
        title: "Success",
        description: "Plan upgrade initiated successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upgrade plan. Please try again.",
        variant: "destructive"
      });
    }
  };

  const plans = plansData?.data || [];
  const invoices = invoicesData?.data || [];
  const currentPlan = plans.find(plan => plan.current);
  const upgradeOptions = plans.filter(plan => !plan.current);

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 flex">
        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          <header className="bg-white shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Plan & Billing</h1>
                <p className="text-gray-600">Manage your subscriptions and billing details effortlessly on this page. Stay updated with your plan options and payment history.</p>
              </div>
              <Button variant="outline" className="text-gray-600">
                Rx
              </Button>
            </div>
          </header>

          <main className="flex-1 p-6 space-y-8">
            {/* Current Plan */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium">Your Current Plan</CardTitle>
                  <Button className="bg-teal-600 hover:bg-teal-700">
                    Upgrade Plan
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {plansLoading ? (
                  <div className="grid grid-cols-4 gap-8">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <div key={index}>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20 mb-1"></div>
                        <div className="h-5 bg-gray-200 rounded animate-pulse w-24"></div>
                      </div>
                    ))}
                  </div>
                ) : plansError ? (
                  <div className="text-center py-8 text-red-500">
                    Error loading plan information
                  </div>
                ) : currentPlan ? (
                  <div className="grid grid-cols-4 gap-8">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Plan Name</p>
                      <p className="font-medium">{currentPlan.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Price</p>
                      <p className="font-medium">{currentPlan.price} (AUD)</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Clinics Included</p>
                      <p className="font-medium">{currentPlan.clinics}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Renewal Date</p>
                      <p className="font-medium">Apr 15, 2026</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No current plan found
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Need More Clinics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Need More Clinics?</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan</TableHead>
                      <TableHead>Clinics</TableHead>
                      <TableHead>Price (AUD)</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {plansLoading ? (
                      Array.from({ length: 3 }).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div></TableCell>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div></TableCell>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-28"></div></TableCell>
                          <TableCell><div className="h-8 bg-gray-200 rounded animate-pulse w-20"></div></TableCell>
                        </TableRow>
                      ))
                    ) : upgradeOptions.length > 0 ? (
                      upgradeOptions.map((option) => (
                        <TableRow key={option.id}>
                          <TableCell className="font-medium">{option.name}</TableCell>
                          <TableCell>{option.clinics}</TableCell>
                          <TableCell>{option.price}</TableCell>
                          <TableCell>
                            {option.name === "Custom" ? (
                              <Button variant="outline">Contact Us</Button>
                            ) : (
                              <Button 
                                className="bg-teal-600 hover:bg-teal-700"
                                onClick={() => handleUpgradePlan(option.id)}
                                disabled={upgradePlanMutation.isPending}
                              >
                                {upgradePlanMutation.isPending ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Upgrading...
                                  </>
                                ) : (
                                  'Upgrade'
                                )}
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                          No upgrade options available
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Billing Details */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium">Billing Details</CardTitle>
                  <div className="space-x-3">
                    <Button variant="outline">Update Card</Button>
                    <Button variant="outline">Update Billing Email</Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-8">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Card on File</p>
                    <p className="font-medium">•••• •••• •••• 4242 (Exp: 03/27)</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Billing Email</p>
                    <p className="font-medium"><EMAIL></p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Download Past Invoices */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Download Past Invoices</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Invoice #</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Download</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoicesLoading ? (
                      Array.from({ length: 3 }).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div></TableCell>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div></TableCell>
                          <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div></TableCell>
                          <TableCell><div className="h-6 bg-gray-200 rounded-full animate-pulse w-16"></div></TableCell>
                          <TableCell><div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div></TableCell>
                        </TableRow>
                      ))
                    ) : invoicesError ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-red-500">
                          Error loading invoices
                        </TableCell>
                      </TableRow>
                    ) : invoices.length > 0 ? (
                      invoices.map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell>{invoice.date}</TableCell>
                          <TableCell>{invoice.invoice}</TableCell>
                          <TableCell>{invoice.amount}</TableCell>
                          <TableCell>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              invoice.status === 'Paid' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.status}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Download className="w-4 h-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                          No invoices found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </Layout>
  );
};

export default PlanBilling;
