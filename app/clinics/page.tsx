'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Filter, Download, Edit, Trash2, Upload, Loader2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AddClinicForm from '@/components/clinics/AddClinicForm';
import UploadExcelForm from '@/components/clinics/UploadExcelForm';
import { useClinics, useDeleteClinic } from '@/hooks/useApi';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/Layout';

const Clinics = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [prescriberRole, setPrescriberRole] = useState('all');
  const { toast } = useToast();

  // Fetch clinics data using React Query
  const { 
    data: clinicsData, 
    isLoading, 
    isError, 
    error 
  } = useClinics({
    search: searchTerm,
    prescriberRole: prescriberRole === 'all' ? undefined : prescriberRole
  });

  const deleteClinicMutation = useDeleteClinic();

  const handleDeleteClinic = async (id: number) => {
    try {
      await deleteClinicMutation.mutateAsync(id);
      toast({
        title: "Success",
        description: "Clinic deleted successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete clinic. Please try again.",
        variant: "destructive"
      });
    }
  };

  const clinics = clinicsData?.data || [];

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">Clinics</h1>
            <p className="text-gray-600">
              You can add clinic locations manually or upload them in bulk using Excel file. Each clinic will be linked to your prescriber profile and can be assigned to team member later.
            </p>
          </div>

          <Tabs defaultValue="all-clinics" className="w-full">
            <TabsList className="grid w-full grid-cols-3 max-w-md">
              <TabsTrigger value="all-clinics">All Clinics</TabsTrigger>
              <TabsTrigger value="upload-excel">Upload via Excel</TabsTrigger>
              <TabsTrigger value="add-manually">Add Manually</TabsTrigger>
            </TabsList>

            <TabsContent value="all-clinics" className="mt-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Clinic List</CardTitle>
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Search"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 w-64"
                        />
                      </div>
                      <Select value={prescriberRole} onValueChange={setPrescriberRole}>
                        <SelectTrigger className="w-32">
                          <Filter className="h-4 w-4 mr-2" />
                          <SelectValue placeholder="Filter by" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All</SelectItem>
                          <SelectItem value="owner">Owner</SelectItem>
                          <SelectItem value="supervising">Supervising</SelectItem>
                          <SelectItem value="visiting">Visiting</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Clinic Name</TableHead>
                        <TableHead>Contact Email</TableHead>
                        <TableHead>Address</TableHead>
                        <TableHead>Prescriber Role</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        // Loading skeleton rows
                        Array.from({ length: 3 }).map((_, index) => (
                          <TableRow key={index}>
                            <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div></TableCell>
                            <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-40"></div></TableCell>
                            <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-48"></div></TableCell>
                            <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div></TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                                <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : isError ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-red-500">
                            Error loading clinics: {error?.message || 'Unknown error'}
                          </TableCell>
                        </TableRow>
                      ) : clinics.length > 0 ? (
                        clinics.map((clinic) => (
                          <TableRow key={clinic.id}>
                            <TableCell className="font-medium">{clinic.name}</TableCell>
                            <TableCell className={clinic.email.includes('Invalid') ? 'text-red-500' : ''}>
                              {clinic.email}
                            </TableCell>
                            <TableCell>{clinic.address}</TableCell>
                            <TableCell>{clinic.prescriberRole}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => handleDeleteClinic(clinic.id)}
                                  disabled={deleteClinicMutation.isPending}
                                >
                                  {deleteClinicMutation.isPending ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                            No clinics found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="upload-excel" className="mt-6">
              <UploadExcelForm />
            </TabsContent>

            <TabsContent value="add-manually" className="mt-6">
              <AddClinicForm />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Clinics;
