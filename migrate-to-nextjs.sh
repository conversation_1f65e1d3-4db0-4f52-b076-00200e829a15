#!/bin/bash

# Next.js Migration Script
# This script helps migrate the React + Vite project to Next.js

echo "🚀 Starting Next.js Migration..."

# Create new Next.js project structure
echo "📁 Creating Next.js directory structure..."

# Create main directories
mkdir -p consultation-club-rx-nextjs/{app,components,lib,hooks,services,types,data,public}
mkdir -p consultation-club-rx-nextjs/components/{ui,clinics}

echo "📦 Copying configuration files..."

# Copy package.json (will need manual editing)
cp package-nextjs.json consultation-club-rx-nextjs/package.json
cp next.config.js consultation-club-rx-nextjs/
cp tailwind.config.ts consultation-club-rx-nextjs/
cp tsconfig.json consultation-club-rx-nextjs/
cp postcss.config.js consultation-club-rx-nextjs/
cp components.json consultation-club-rx-nextjs/

echo "🎨 Copying styles..."
cp app/globals.css consultation-club-rx-nextjs/app/

echo "📄 Copying app pages..."
cp app/*.tsx consultation-club-rx-nextjs/app/
cp -r app/dashboard consultation-club-rx-nextjs/app/
cp -r app/clinics consultation-club-rx-nextjs/app/
cp -r app/inventory consultation-club-rx-nextjs/app/
cp -r app/logs-activity consultation-club-rx-nextjs/app/
cp -r app/users-access consultation-club-rx-nextjs/app/
cp -r app/plan-billing consultation-club-rx-nextjs/app/
cp -r app/settings consultation-club-rx-nextjs/app/
cp -r app/notifications consultation-club-rx-nextjs/app/
cp -r app/support consultation-club-rx-nextjs/app/

echo "🧩 Copying components..."
cp components/*.tsx consultation-club-rx-nextjs/components/

# Copy UI components (need to copy from src/components/ui)
echo "Copying UI components from src/components/ui to consultation-club-rx-nextjs/components/ui"
cp -r src/components/ui/* consultation-club-rx-nextjs/components/ui/

# Copy clinic components
echo "Copying clinic components from src/components/clinics to consultation-club-rx-nextjs/components/clinics"
cp -r src/components/clinics/* consultation-club-rx-nextjs/components/clinics/

echo "🔧 Copying utilities and services..."
cp lib/utils.ts consultation-club-rx-nextjs/lib/
cp -r src/hooks/* consultation-club-rx-nextjs/hooks/
cp -r src/services/* consultation-club-rx-nextjs/services/
cp -r src/types/* consultation-club-rx-nextjs/types/
cp -r src/data/* consultation-club-rx-nextjs/data/

echo "🖼️ Copying public assets..."
cp -r public/* consultation-club-rx-nextjs/public/

echo "📚 Copying documentation..."
cp *.md consultation-club-rx-nextjs/

echo "✅ File copying complete!"

echo ""
echo "🔄 Next steps to complete the migration:"
echo ""
echo "1. Navigate to the new directory:"
echo "   cd consultation-club-rx-nextjs"
echo ""
echo "2. Install dependencies:"
echo "   npm install"
echo ""
echo "3. Update any remaining import paths in components if needed"
echo ""
echo "4. Start the development server:"
echo "   npm run dev"
echo ""
echo "5. Test all functionality to ensure everything works"
echo ""
echo "6. Build for production:"
echo "   npm run build"
echo ""
echo "🎉 Migration setup complete!"
echo ""
echo "📋 Manual tasks remaining:"
echo "- Update any hardcoded import paths if needed"
echo "- Test all pages and functionality"
echo "- Verify API integration works"
echo "- Check responsive design"
echo "- Test error handling"
echo ""
echo "The new Next.js project is ready in: consultation-club-rx-nextjs/"
