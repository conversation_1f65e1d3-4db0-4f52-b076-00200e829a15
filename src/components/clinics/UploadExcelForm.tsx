
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardT<PERSON>le } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Download, Check, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useBulkUploadClinics } from '@/hooks/useApi';

const UploadExcelForm = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const { toast } = useToast();
  const bulkUploadMutation = useBulkUploadClinics();

  const handleFileSelect = (file: File) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Error",
        description: "Please select a valid Excel file (.xls or .xlsx).",
        variant: "destructive"
      });
      return;
    }

    setSelectedFile(file);
    toast({
      title: "Success",
      description: "File selected successfully!",
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "Error",
        description: "Please select a file to upload.",
        variant: "destructive"
      });
      return;
    }

    try {
      const result = await bulkUploadMutation.mutateAsync(selectedFile);
      toast({
        title: "Success",
        description: `Excel file uploaded and processed successfully! ${result.data.processed} clinics processed.`,
      });
      setSelectedFile(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const downloadTemplate = () => {
    // In a real app, this would download an actual Excel template
    toast({
      title: "Download Started",
      description: "XLS template download has started.",
    });
  };

  return (
    <div className="space-y-6">
      <Card className="max-w-4xl">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            <div
              className={`border-2 border-dashed rounded-lg p-12 transition-colors ${
                isDragOver
                  ? 'border-teal-500 bg-teal-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 text-gray-400">
                  <Upload className="w-full h-full" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    Drag and drop file, or click to upload
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Supports .xls or .xlsx files
                  </p>
                </div>
                <input
                  type="file"
                  accept=".xls,.xlsx"
                  onChange={handleFileInputChange}
                  className="hidden"
                  id="file-upload"
                />
                <Button
                  type="button"
                  className="bg-teal-600 hover:bg-teal-700"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  Choose File
                </Button>
              </div>
            </div>

            {selectedFile && (
              <div className="flex items-center justify-center gap-2 text-green-600">
                <Check className="w-4 h-4" />
                <span className="text-sm font-medium">{selectedFile.name}</span>
              </div>
            )}

            {selectedFile && (
              <Button
                onClick={handleUpload}
                className="bg-teal-600 hover:bg-teal-700"
                disabled={bulkUploadMutation.isPending}
              >
                {bulkUploadMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  'Upload File'
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="max-w-4xl">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Download className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-medium text-gray-900">Download XLS Template</h3>
            </div>
            
            <div className="space-y-3">
              <p className="text-sm font-medium text-gray-900">Template Includes:</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Clinic Name</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Address</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Primary Contact Name</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Primary Contact Email</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600">Your Role (Owner / Visiting / Supervising)</span>
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={downloadTemplate}
              className="w-fit"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Template
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UploadExcelForm;
