
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useCreateClinic } from '@/hooks/useApi';
import { Loader2 } from 'lucide-react';

const AddClinicForm = () => {
  const [formData, setFormData] = useState({
    clinicName: '',
    contactEmail: '',
    contactName: '',
    prescriberRole: '',
    address: ''
  });
  const { toast } = useToast();
  const createClinicMutation = useCreateClinic();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.clinicName || !formData.contactEmail || !formData.contactName || !formData.prescriberRole || !formData.address) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.contactEmail)) {
      toast({
        title: "Error",
        description: "Please enter a valid email address.",
        variant: "destructive"
      });
      return;
    }

    try {
      await createClinicMutation.mutateAsync({
        name: formData.clinicName,
        email: formData.contactEmail,
        contactName: formData.contactName,
        prescriberRole: formData.prescriberRole,
        address: formData.address
      });

      toast({
        title: "Success",
        description: "Clinic added successfully!",
      });

      // Reset form
      setFormData({
        clinicName: '',
        contactEmail: '',
        contactName: '',
        prescriberRole: '',
        address: ''
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add clinic. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      clinicName: '',
      contactEmail: '',
      contactName: '',
      prescriberRole: '',
      address: ''
    });
  };

  return (
    <Card className="max-w-4xl">
      <CardHeader>
        <CardTitle>Add Clinic Manually</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="clinicName">Clinic Name</Label>
              <Input
                id="clinicName"
                placeholder="Enter Clinic Name"
                value={formData.clinicName}
                onChange={(e) => handleInputChange('clinicName', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contactEmail">Contact Email</Label>
              <Input
                id="contactEmail"
                type="email"
                placeholder="Enter Contact Email"
                value={formData.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contactName">Contact Name</Label>
              <Input
                id="contactName"
                placeholder="Enter Contact Name"
                value={formData.contactName}
                onChange={(e) => handleInputChange('contactName', e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="prescriberRole">Prescriber Role</Label>
              <Select value={formData.prescriberRole} onValueChange={(value) => handleInputChange('prescriberRole', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="owner">Owner</SelectItem>
                  <SelectItem value="visiting">Visiting</SelectItem>
                  <SelectItem value="supervising">Supervising</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                placeholder="Enter Clinic Address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-end gap-4 pt-6">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-teal-600 hover:bg-teal-700"
              disabled={createClinicMutation.isPending}
            >
              {createClinicMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default AddClinicForm;
