import axios from 'axios';
import mockData from '../data/mockData.json';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens (when needed)
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Simulate API delay for realistic behavior
const simulateDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions that simulate real API calls
export const authAPI = {
  getProfile: async () => {
    await simulateDelay();
    return { data: mockData.user };
  },
  
  updateProfile: async (profileData: any) => {
    await simulateDelay();
    return { data: { ...mockData.user, ...profileData } };
  },
};

export const dashboardAPI = {
  getStats: async () => {
    await simulateDelay();
    return { data: mockData.dashboardStats };
  },
  
  getRecentActivity: async (limit: number = 5) => {
    await simulateDelay();
    return { 
      data: mockData.recentActivity.slice(0, limit),
      total: mockData.recentActivity.length
    };
  },
};

export const clinicsAPI = {
  getClinics: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    prescriberRole?: string;
  }) => {
    await simulateDelay();
    
    let filteredClinics = [...mockData.clinics];
    
    // Apply search filter
    if (params?.search) {
      const searchTerm = params.search.toLowerCase();
      filteredClinics = filteredClinics.filter(clinic =>
        clinic.name.toLowerCase().includes(searchTerm) ||
        clinic.email.toLowerCase().includes(searchTerm) ||
        clinic.address.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply role filter
    if (params?.prescriberRole && params.prescriberRole !== 'all') {
      filteredClinics = filteredClinics.filter(clinic =>
        clinic.prescriberRole.toLowerCase() === params.prescriberRole?.toLowerCase()
      );
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedClinics = filteredClinics.slice(startIndex, endIndex);
    
    return {
      data: paginatedClinics,
      pagination: {
        page,
        limit,
        total: filteredClinics.length,
        totalPages: Math.ceil(filteredClinics.length / limit)
      }
    };
  },
  
  createClinic: async (clinicData: any) => {
    await simulateDelay();
    const newClinic = {
      id: mockData.clinics.length + 1,
      ...clinicData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    return { data: newClinic };
  },
  
  updateClinic: async (id: number, clinicData: any) => {
    await simulateDelay();
    const clinic = mockData.clinics.find(c => c.id === id);
    if (!clinic) {
      throw new Error('Clinic not found');
    }
    const updatedClinic = {
      ...clinic,
      ...clinicData,
      updatedAt: new Date().toISOString()
    };
    return { data: updatedClinic };
  },
  
  deleteClinic: async (id: number) => {
    await simulateDelay();
    const clinic = mockData.clinics.find(c => c.id === id);
    if (!clinic) {
      throw new Error('Clinic not found');
    }
    return { data: { message: 'Clinic deleted successfully' } };
  },
  
  bulkUpload: async (file: File) => {
    await simulateDelay(2000); // Longer delay for file upload
    // Simulate processing Excel file
    return { 
      data: { 
        message: 'Excel file processed successfully',
        processed: 5,
        errors: 0
      }
    };
  },
};

export const usersAPI = {
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
  }) => {
    await simulateDelay();
    
    let filteredUsers = [...mockData.users];
    
    // Apply search filter
    if (params?.search) {
      const searchTerm = params.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        user.role.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply role filter
    if (params?.role && params.role !== 'all') {
      filteredUsers = filteredUsers.filter(user =>
        user.role.toLowerCase() === params.role?.toLowerCase()
      );
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      data: paginatedUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      }
    };
  },
  
  createUser: async (userData: any) => {
    await simulateDelay();
    const newUser = {
      id: mockData.users.length + 1,
      ...userData,
      lastActive: new Date().toLocaleDateString(),
      lastActiveTimestamp: new Date().toISOString(),
      status: 'Active'
    };
    return { data: newUser };
  },
  
  updateUser: async (id: number, userData: any) => {
    await simulateDelay();
    const user = mockData.users.find(u => u.id === id);
    if (!user) {
      throw new Error('User not found');
    }
    const updatedUser = {
      ...user,
      ...userData,
      lastActiveTimestamp: new Date().toISOString()
    };
    return { data: updatedUser };
  },
  
  deleteUser: async (id: number) => {
    await simulateDelay();
    const user = mockData.users.find(u => u.id === id);
    if (!user) {
      throw new Error('User not found');
    }
    return { data: { message: 'User deleted successfully' } };
  },
};

export const billingAPI = {
  getPlans: async () => {
    await simulateDelay();
    return { data: mockData.billingPlans };
  },
  
  getInvoices: async (params?: {
    page?: number;
    limit?: number;
  }) => {
    await simulateDelay();
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedInvoices = mockData.invoices.slice(startIndex, endIndex);
    
    return {
      data: paginatedInvoices,
      pagination: {
        page,
        limit,
        total: mockData.invoices.length,
        totalPages: Math.ceil(mockData.invoices.length / limit)
      }
    };
  },
  
  upgradePlan: async (planId: number) => {
    await simulateDelay();
    const plan = mockData.billingPlans.find(p => p.id === planId);
    if (!plan) {
      throw new Error('Plan not found');
    }
    return { 
      data: { 
        message: 'Plan upgrade initiated successfully',
        plan: plan.name
      }
    };
  },
};

export const supportAPI = {
  submitTicket: async (ticketData: any) => {
    await simulateDelay();
    const ticket = {
      id: Date.now(),
      ...ticketData,
      status: 'Open',
      createdAt: new Date().toISOString()
    };
    return { data: ticket };
  },
};

export default api;
