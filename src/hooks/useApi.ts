import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  authAPI, 
  dashboardAPI, 
  clinicsAPI, 
  usersAPI, 
  billingAPI, 
  supportAPI 
} from '../services/api';

// Query Keys
export const queryKeys = {
  auth: {
    profile: ['auth', 'profile'] as const,
  },
  dashboard: {
    stats: ['dashboard', 'stats'] as const,
    recentActivity: (limit?: number) => ['dashboard', 'recentActivity', limit] as const,
  },
  clinics: {
    all: ['clinics'] as const,
    list: (params?: any) => ['clinics', 'list', params] as const,
    detail: (id: number) => ['clinics', 'detail', id] as const,
  },
  users: {
    all: ['users'] as const,
    list: (params?: any) => ['users', 'list', params] as const,
    detail: (id: number) => ['users', 'detail', id] as const,
  },
  billing: {
    plans: ['billing', 'plans'] as const,
    invoices: (params?: any) => ['billing', 'invoices', params] as const,
  },
};

// Auth Hooks
export const useProfile = () => {
  return useQuery({
    queryKey: queryKeys.auth.profile,
    queryFn: authAPI.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authAPI.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile });
    },
  });
};

// Dashboard Hooks
export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.dashboard.stats,
    queryFn: dashboardAPI.getStats,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useRecentActivity = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.dashboard.recentActivity(limit),
    queryFn: () => dashboardAPI.getRecentActivity(limit),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Clinics Hooks
export const useClinics = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  prescriberRole?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.clinics.list(params),
    queryFn: () => clinicsAPI.getClinics(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateClinic = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: clinicsAPI.createClinic,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clinics.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats });
    },
  });
};

export const useUpdateClinic = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => 
      clinicsAPI.updateClinic(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clinics.all });
    },
  });
};

export const useDeleteClinic = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: clinicsAPI.deleteClinic,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clinics.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats });
    },
  });
};

export const useBulkUploadClinics = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: clinicsAPI.bulkUpload,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.clinics.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats });
    },
  });
};

// Users Hooks
export const useUsers = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.users.list(params),
    queryFn: () => usersAPI.getUsers(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: usersAPI.createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => 
      usersAPI.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: usersAPI.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
    },
  });
};

// Billing Hooks
export const useBillingPlans = () => {
  return useQuery({
    queryKey: queryKeys.billing.plans,
    queryFn: billingAPI.getPlans,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useInvoices = (params?: {
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: queryKeys.billing.invoices(params),
    queryFn: () => billingAPI.getInvoices(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpgradePlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: billingAPI.upgradePlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.billing.plans });
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile });
    },
  });
};

// Support Hooks
export const useSubmitSupportTicket = () => {
  return useMutation({
    mutationFn: supportAPI.submitTicket,
  });
};

// Loading and Error States Helper
export const useApiState = (queries: any[]) => {
  const isLoading = queries.some(query => query.isLoading);
  const isError = queries.some(query => query.isError);
  const errors = queries.filter(query => query.isError).map(query => query.error);
  
  return {
    isLoading,
    isError,
    errors,
  };
};
