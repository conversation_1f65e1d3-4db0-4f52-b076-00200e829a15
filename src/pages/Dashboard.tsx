import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Building2,
  Package,
  Plus,
  Clock,
  Users,
  FileText,
  BarChart3,
  Settings,
  Bell,
  HelpCircle,
  LogOut,
  Eye,
  Edit,
  Loader2
} from "lucide-react";
import { useDashboardStats, useRecentActivity, useProfile } from '@/hooks/useApi';

const Dashboard = () => {
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);

  // Fetch data using React Query hooks
  const { data: profile, isLoading: profileLoading } = useProfile();
  const { data: stats, isLoading: statsLoading } = useDashboardStats();
  const { data: activityData, isLoading: activityLoading } = useRecentActivity(3);

  const actionCards = [
    {
      title: "Add Clinic",
      icon: Building2,
      description: ""
    },
    {
      title: "View Full Inventory Log",
      icon: FileText,
      description: ""
    },
    {
      title: "Manage Your Plan",
      icon: BarChart3,
      description: ""
    }
  ];

  // Create stats cards from API data
  const statsCards = stats ? [
    {
      title: "Clinics Supervised",
      value: stats.data.clinicsSupervised.toString(),
      icon: Building2,
      color: "bg-teal-100 text-teal-600"
    },
    {
      title: "Products Checked Out",
      value: stats.data.productsCheckedOut.toString(),
      icon: Clock,
      color: "bg-teal-100 text-teal-600"
    },
    {
      title: "Inventory Added",
      value: stats.data.inventoryAdded.toString(),
      icon: Edit,
      color: "bg-teal-100 text-teal-600"
    }
  ] : [];

  const recentActivity = activityData?.data || [];

  // Show loading state
  if (profileLoading || statsLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      <header className="bg-white shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {profile?.data?.name || 'User'}
            </h1>
            <p className="text-gray-600">Here's a snapshot of your clinic activity this week.</p>
          </div>
          <Button variant="outline" className="text-gray-600">
            Rx
          </Button>
        </div>
      </header>

      <main className="flex-1 p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {statsLoading ? (
            // Loading skeleton for stats cards
            Array.from({ length: 3 }).map((_, index) => (
              <Card key={index} className="relative">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                      <div className="h-8 bg-gray-200 rounded animate-pulse w-16"></div>
                    </div>
                    <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            statsCards.map((card, index) => (
              <Card key={index} className="relative">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-2">{card.title}</p>
                      <p className="text-3xl font-bold text-gray-900">{card.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg ${card.color}`}>
                      <card.icon className="w-6 h-6" />
                    </div>
                  </div>
                  {index === 2 && (
                    <Button variant="link" className="mt-2 p-0 h-auto text-sm text-gray-600">
                      Go to Inventory
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {actionCards.map((card, index) => (
            <Card key={index} className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gray-100 rounded-lg mb-4">
                    <card.icon className="w-6 h-6 text-gray-600" />
                  </div>
                  <h3 className="font-medium text-gray-900">{card.title}</h3>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
              <Button variant="link" className="text-sm text-gray-600">
                View All Logs
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activityLoading ? (
                // Loading skeleton for activity items
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
                      <div className="h-3 bg-gray-200 rounded animate-pulse w-48"></div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-20"></div>
                  </div>
                ))
              ) : recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div key={activity.id || index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                    <div>
                      <p className="font-medium text-gray-900">{activity.user}</p>
                      <p className="text-sm text-gray-600">{activity.action}</p>
                    </div>
                    <p className="text-sm text-gray-500">{activity.time}</p>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No recent activity found</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </main>

      <footer className="bg-white border-t p-6">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <p>© 2025 Consultation.club Rx. All rights reserved.</p>
          <div className="flex space-x-6">
            <a href="#" className="hover:text-gray-900">Privacy</a>
            <a href="#" className="hover:text-gray-900">Terms</a>
            <a href="#" className="hover:text-gray-900">Contact</a>
          </div>
        </div>
      </footer>

      {/* Welcome Modal */}
      <Dialog open={showWelcomeModal} onOpenChange={setShowWelcomeModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Welcome to Consultation.Club Rx</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <p className="text-gray-600">
              Thanks for joining the platform. You now have visibility over $4 product access across your clinics. Here's how to get started.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Building2 className="w-5 h-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Add Your Clinics</h3>
                    <Button variant="link" className="text-sm p-0 h-auto">
                      Go to Clinics
                    </Button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Link each location you supervise and assign your role.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Package className="w-5 h-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">Track Inventory</h3>
                    <Button variant="link" className="text-sm p-0 h-auto">
                      Go to Inventory
                    </Button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage your product inventory across all clinics.
                  </p>
                </div>
              </div>
            </div>
            
            <Button className="w-full">Get Started</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Dashboard;