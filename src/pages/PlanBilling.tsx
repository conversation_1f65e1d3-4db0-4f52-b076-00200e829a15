
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Building2,
  Package,
  Clock,
  Users,
  FileText,
  BarChart3,
  Settings,
  Bell,
  HelpCircle,
  LogOut,
  Download,
  Loader2,
  Check
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useBillingPlans, useInvoices, useUpgradePlan } from '@/hooks/useApi';
import { useToast } from '@/hooks/use-toast';

const PlanBilling = () => {
  const { toast } = useToast();

  // Fetch billing data using React Query
  const {
    data: plansData,
    isLoading: plansLoading,
    isError: plansError
  } = useBillingPlans();

  const {
    data: invoicesData,
    isLoading: invoicesLoading,
    isError: invoicesError
  } = useInvoices();

  const upgradePlanMutation = useUpgradePlan();

  const handleUpgradePlan = async (planId: number) => {
    try {
      await upgradePlanMutation.mutateAsync(planId);
      toast({
        title: "Success",
        description: "Plan upgrade initiated successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upgrade plan. Please try again.",
        variant: "destructive"
      });
    }
  };

  const plans = plansData?.data || [];
  const invoices = invoicesData?.data || [];
  const currentPlan = plans.find(plan => plan.current);
  const upgradeOptions = plans.filter(plan => !plan.current);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Plan & Billing</h1>
              <p className="text-gray-600">Manage your subscriptions and billing details effortlessly on this page. Stay updated with your plan options and payment history.</p>
            </div>
            <Button variant="outline" className="text-gray-600">
              Rx
            </Button>
          </div>
        </header>

        <main className="flex-1 p-6 space-y-8">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-medium">Your Current Plan</CardTitle>
                <Button className="bg-teal-600 hover:bg-teal-700">
                  Upgrade Plan
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-8">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Plan Name</p>
                  <p className="font-medium">Starter</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">Price</p>
                  <p className="font-medium">$1,500/year (AUD)</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">Clinics Included</p>
                  <p className="font-medium">Up to 10</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">Renewal Date</p>
                  <p className="font-medium">Apr 15, 2026</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Need More Clinics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Need More Clinics?</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan</TableHead>
                    <TableHead>Clinics</TableHead>
                    <TableHead>Price (AUD)</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {upgradeOptions.map((option, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{option.plan}</TableCell>
                      <TableCell>{option.clinics}</TableCell>
                      <TableCell>{option.price}</TableCell>
                      <TableCell>
                        {option.plan === "Custom" ? (
                          <Button variant="outline">Contact Us</Button>
                        ) : (
                          <Button className="bg-teal-600 hover:bg-teal-700">Upgrade</Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Billing Details */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-medium">Billing Details</CardTitle>
                <div className="space-x-3">
                  <Button variant="outline">Update Card</Button>
                  <Button variant="outline">Update Billing Email</Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Card on File</p>
                  <p className="font-medium">•••• •••• •••• 4242 (Exp: 03/27)</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">Billing Email</p>
                  <p className="font-medium"><EMAIL></p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Download Past Invoices */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Download Past Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Download</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice, index) => (
                    <TableRow key={index}>
                      <TableCell>{invoice.date}</TableCell>
                      <TableCell>{invoice.invoice}</TableCell>
                      <TableCell>{invoice.amount}</TableCell>
                      <TableCell>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {invoice.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Need to make changes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Need to make a changes?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="text-gray-600">If you need to downgrade or cancel your plan, please contact our support team so we can assist you.</p>
                <Button variant="outline">Contact Support</Button>
              </div>
            </CardContent>
          </Card>
        </main>

        <footer className="bg-white border-t p-6">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <p>© 2025 Consultation.club Rx. All rights reserved.</p>
            <div className="flex space-x-6">
              <a href="#" className="hover:text-gray-900">Privacy</a>
              <a href="#" className="hover:text-gray-900">Terms</a>
              <a href="#" className="hover:text-gray-900">Contact</a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default PlanBilling;
