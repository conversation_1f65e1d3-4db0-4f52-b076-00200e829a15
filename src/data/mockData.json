{"user": {"id": 1, "name": "Dr. <PERSON>", "email": "<EMAIL>", "role": "Prescriber", "avatar": "/placeholder.svg"}, "dashboardStats": {"clinicsSupervised": 5, "productsCheckedOut": 1, "inventoryAdded": 8}, "recentActivity": [{"id": 1, "user": "Nurse <PERSON>", "action": "Checked out 1 x Botox", "time": "Today, 2:30 PM", "timestamp": "2025-06-25T14:30:00Z"}, {"id": 2, "user": "Admin <PERSON>", "action": "Added new inventory", "time": "Today, 11:15 AM", "timestamp": "2025-06-25T11:15:00Z"}, {"id": 3, "user": "Nurse <PERSON>", "action": "Checked out 2 x Dysport", "time": "Yesterday, 4:45 PM", "timestamp": "2025-06-24T16:45:00Z"}, {"id": 4, "user": "Dr. <PERSON>", "action": "Updated clinic information", "time": "Yesterday, 2:20 PM", "timestamp": "2025-06-24T14:20:00Z"}, {"id": 5, "user": "Nurse <PERSON>", "action": "Checked out 3 x Juvederm", "time": "2 days ago, 3:15 PM", "timestamp": "2025-06-23T15:15:00Z"}], "clinics": [{"id": 1, "name": "Aesthetic Clinic Co.", "email": "<EMAIL>", "contactName": "<PERSON>", "address": "2972 Westheimer Rd. Santa Ana, Illinois 85486", "prescriberRole": "Supervising", "createdAt": "2025-01-15T10:00:00Z", "updatedAt": "2025-06-20T14:30:00Z"}, {"id": 2, "name": "Beauty Med Spa", "email": "<EMAIL>", "contactName": "<PERSON>", "address": "3891 Ranchview Dr. <PERSON>, California 62639", "prescriberRole": "Owner", "createdAt": "2025-02-10T09:30:00Z", "updatedAt": "2025-06-18T11:45:00Z"}, {"id": 3, "name": "Elite Wellness Center", "email": "<EMAIL>", "contactName": "<PERSON>", "address": "1234 Medical Plaza, Los Angeles, CA 90210", "prescriberRole": "Visiting", "createdAt": "2025-03-05T16:20:00Z", "updatedAt": "2025-06-22T09:15:00Z"}, {"id": 4, "name": "Rejuvenation Clinic", "email": "<EMAIL>", "contactName": "Dr. <PERSON>", "address": "5678 Health Ave, Miami, FL 33101", "prescriberRole": "Supervising", "createdAt": "2025-04-12T13:45:00Z", "updatedAt": "2025-06-21T16:30:00Z"}, {"id": 5, "name": "Harmony Aesthetics", "email": "<EMAIL>", "contactName": "Dr. <PERSON>", "address": "9012 Beauty Blvd, New York, NY 10001", "prescriberRole": "Owner", "createdAt": "2025-05-08T11:20:00Z", "updatedAt": "2025-06-23T12:00:00Z"}], "users": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "role": "Nurse", "clinics": ["Aesthetic Clinic Co.", "Beauty Med Spa"], "clinicIds": [1, 2], "lastActive": "Apr 15, 2025", "lastActiveTimestamp": "2025-04-15T14:30:00Z", "status": "Active"}, {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "role": "Admin", "clinics": ["Beauty Med Spa", "Elite Wellness Center"], "clinicIds": [2, 3], "lastActive": "Jun 20, 2025", "lastActiveTimestamp": "2025-06-20T16:45:00Z", "status": "Active"}, {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "role": "Nurse", "clinics": ["Elite Wellness Center"], "clinicIds": [3], "lastActive": "Jun 22, 2025", "lastActiveTimestamp": "2025-06-22T10:15:00Z", "status": "Active"}, {"id": 4, "name": "Dr. <PERSON>", "email": "<EMAIL>", "role": "Doctor", "clinics": ["Rejuvenation Clinic"], "clinicIds": [4], "lastActive": "Jun 24, 2025", "lastActiveTimestamp": "2025-06-24T13:20:00Z", "status": "Active"}], "billingPlans": [{"id": 1, "name": "Starter", "clinics": "1 - 10", "price": "$1,200/year", "priceValue": 1200, "features": ["Up to 10 clinics", "Basic inventory tracking", "Standard support", "Monthly reports"], "current": true}, {"id": 2, "name": "Professional", "clinics": "11 - 50", "price": "$2,500/year", "priceValue": 2500, "features": ["Up to 50 clinics", "Advanced inventory tracking", "Priority support", "Weekly reports", "API access"], "current": false}, {"id": 3, "name": "Enterprise", "clinics": "51 - 100", "price": "$3,900/year", "priceValue": 3900, "features": ["Up to 100 clinics", "Full inventory management", "24/7 support", "Daily reports", "Full API access", "Custom integrations"], "current": false}, {"id": 4, "name": "Custom", "clinics": "100+", "price": "Let's talk", "priceValue": null, "features": ["Unlimited clinics", "Enterprise features", "Dedicated support", "Custom reporting", "White-label options"], "current": false}], "invoices": [{"id": 1, "date": "Apr 15, 2026", "invoice": "# INV-021", "amount": "$1,500", "amountValue": 1500, "status": "Paid", "dueDate": "Apr 30, 2026", "planName": "Starter"}, {"id": 2, "date": "Apr 15, 2025", "invoice": "# INV-020", "amount": "$1,200", "amountValue": 1200, "status": "Paid", "dueDate": "Apr 30, 2025", "planName": "Starter"}, {"id": 3, "date": "Apr 15, 2024", "invoice": "# INV-019", "amount": "$1,200", "amountValue": 1200, "status": "Paid", "dueDate": "Apr 30, 2024", "planName": "Starter"}]}