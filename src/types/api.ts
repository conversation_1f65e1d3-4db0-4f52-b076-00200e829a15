// User types
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

// Dashboard types
export interface DashboardStats {
  clinicsSupervised: number;
  productsCheckedOut: number;
  inventoryAdded: number;
}

export interface RecentActivity {
  id: number;
  user: string;
  action: string;
  time: string;
  timestamp: string;
}

// Clinic types
export interface Clinic {
  id: number;
  name: string;
  email: string;
  contactName: string;
  address: string;
  prescriberRole: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateClinicData {
  name: string;
  email: string;
  contactName: string;
  address: string;
  prescriberRole: string;
}

// User management types
export interface UserData {
  id: number;
  name: string;
  email: string;
  role: string;
  clinics: string[];
  clinicIds: number[];
  lastActive: string;
  lastActiveTimestamp: string;
  status: string;
}

export interface CreateUserData {
  name: string;
  email: string;
  role: string;
  clinicIds: number[];
}

// Billing types
export interface BillingPlan {
  id: number;
  name: string;
  clinics: string;
  price: string;
  priceValue: number | null;
  features: string[];
  current: boolean;
}

export interface Invoice {
  id: number;
  date: string;
  invoice: string;
  amount: string;
  amountValue: number;
  status: string;
  dueDate: string;
  planName: string;
}

// Support types
export interface SupportTicket {
  fullName: string;
  clinic: string;
  email: string;
  subject: string;
  message: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Query parameter types
export interface ClinicsQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  prescriberRole?: string;
}

export interface UsersQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
}

export interface InvoicesQueryParams {
  page?: number;
  limit?: number;
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}
